"""
HTML formatters for SNCF train data.

This module handles formatting of journey, departure, and arrival data into HTML.
"""
from typing import Dict, List, Optional, Tuple

from .datetime_utils import format_datetime, format_duration
from .price_utils import extract_price_info
from .template_utils import get_html_template, get_station_schedule_template, get_transfer_badge
from .train_logos import get_train_logos, detect_train_type


def get_carrier_and_price(journey: Dict, selected_tariff: str = "Tarif Normal") -> Tuple[str, str]:
    """
    Extract carrier and price information from journey.

    Args:
        journey: Journey data
        selected_tariff: The selected tariff type

    Returns:
        Tuple of (carrier_html, price_html)
    """
    carrier = ""
    price = "N/A"

    # Find the first public transport section with carrier info
    for section in journey.get("sections", []):
        if "transport_info" in section:
            transport_info = section["transport_info"]

            # Get train information for intelligent detection
            network = transport_info.get("network", "")
            commercial_mode = transport_info.get("commercial_mode", "")
            trip_short_name = transport_info.get("trip_short_name", "")

            # Use intelligent train type detection
            train_type = detect_train_type(network, commercial_mode, trip_short_name)
            train_logos = get_train_logos()
            carrier = train_logos[train_type]
            break

    # Extract price information from journey_prices field (new API format)
    journey_prices = journey.get("journey_prices", {})
    if journey_prices:
        price = extract_price_info(journey_prices, selected_tariff)

    return (carrier, price)


def format_journey_data(journey_data: Dict, selected_tariff: str = "Tarif Normal") -> str:
    """
    Format journey data from the get_journey tool into HTML.

    Args:
        journey_data: The journey data from the get_journey tool
        selected_tariff: The selected tariff type

    Returns:
        Formatted HTML string for display in Streamlit
    """
    if not journey_data or "journeys" not in journey_data:
        return "<p>No journey data available</p>"

    # Get the HTML template
    template = get_html_template()
    if template.startswith("<p>Error"):
        return template

    # Extract journey information
    journeys = journey_data.get("journeys", [])
    if not journeys:
        return "<p>No journeys found in data</p>"

    # Extract origin and destination from the first journey
    from_station = journey_data.get("from_station", "Départ")
    to_station = journey_data.get("to_station", "Arrivée")

    # Generate table rows for all journeys
    table_rows = []
    for journey in journeys:
        # Format journey data
        departure_time = format_datetime(journey.get("departure", ""))
        arrival_time = format_datetime(journey.get("arrival", ""))
        duration = format_duration(journey.get("duration", 0))
        transfer_badge = get_transfer_badge(journey.get("nb_transfers", 0))
        carrier_info, price_info = get_carrier_and_price(journey, selected_tariff)

        # Add row to table
        table_rows.append(f"""
        <tr class="journey-row">
            <td>
                <div class="journey-time departure">{departure_time[0]}</div>
                <div class="journey-station">{from_station}</div>
            </td>
            <td>
                <div class="journey-time arrival">{arrival_time[0]}</div>
                <div class="journey-station">{to_station}</div>
            </td>
            <td class="duration">{duration}</td>
            <td class="correspondence-column">{transfer_badge}</td>
            <td class="carrier-column">{carrier_info}</td>
            <td class="price-column">
                <div class="price-range">{price_info}</div>
            </td>
        </tr>""")

    # Insert the rows into the template
    template = template.replace('</tr>\n    </tbody>', f'{"".join(table_rows)}\n    </tbody>')

    # Replace the static subtitle with dynamic station names
    template = template.replace('Paris - Marseille', f'{from_station} - {to_station}')

    return template


def format_station_schedule_data(schedule_data: Dict, schedule_type: str = "departures") -> str:
    """
    Unified function to format departures or arrivals data into HTML.

    Args:
        schedule_data: The schedule data from get_departures or get_arrivals tool
        schedule_type: "departures" or "arrivals"

    Returns:
        Formatted HTML string for display in Streamlit
    """
    # Determine the data key and labels based on schedule type
    if schedule_type == "arrivals":
        data_key = "arrivals"
        title = "Prochaines arrivées"
        destination_label = "Provenance"
        theme_class = "arrivals-theme"
    else:  # departures
        data_key = "departures"
        title = "Prochains départs"
        destination_label = "Destination"
        theme_class = "departures-theme"

    # Get the HTML template
    template = get_station_schedule_template()
    if template.startswith("<p>Error"):
        return template

    # Extract schedule information
    schedules = schedule_data.get(data_key, [])
    if not schedules:
        return f"<p>No {schedule_type} found in data</p>"

    # Extract station name
    station_name = schedule_data.get("station_name", "Station")

    # Replace template placeholders
    template = template.replace('SCHEDULE_THEME_CLASS', theme_class)
    template = template.replace('SCHEDULE_TITLE', title)
    template = template.replace('STATION_NAME', station_name)
    template = template.replace('DESTINATION_ORIGIN_LABEL', destination_label)

    # Generate table rows for all schedules
    table_rows = []
    for schedule in schedules:
        # Extract schedule information
        display_info = schedule.get("display_informations", {})
        stop_date_time = schedule.get("stop_date_time", {})

        # Format time based on schedule type
        if schedule_type == "arrivals":
            time_str = format_datetime(stop_date_time.get("arrival_date_time", ""))
        else:  # departures
            time_str = format_datetime(stop_date_time.get("departure_date_time", ""))

        # Get train number from trip_short_name (new API format)
        train_number = display_info.get("trip_short_name", display_info.get("headsign", ""))

        # Get destination/origin information
        destination_origin = ""
        if schedule_type == "arrivals":
            # For arrivals, we need to determine the origin
            route = schedule.get("route", {})
            route_name = route.get("name", "")
            direction = display_info.get("direction", "")

            if route_name and " - " in route_name:
                # Parse route name to find origin
                parts = route_name.split(" - ")
                # Remove parentheses from direction for comparison
                clean_direction = direction.replace("(", "").replace(")", "")
                # Find which part is NOT the direction (destination)
                for part in parts:
                    if part.strip() != clean_direction:
                        destination_origin = part.strip()
                        break
                if not destination_origin:
                    destination_origin = parts[0]  # Fallback to first part
            else:
                # Fallback to route direction name for TER trains
                destination_origin = route.get("direction", {}).get("name", direction)
        else:  # departures
            destination_origin = display_info.get("direction", "")

        # Remove city names in parentheses from destinations
        if "(" in destination_origin and ")" in destination_origin:
            destination_origin = destination_origin.split("(")[0].strip()

        # Get train type and logo
        network = display_info.get("network", "")
        commercial_mode = display_info.get("commercial_mode", "")
        train_type = detect_train_type(network, commercial_mode, train_number)
        train_logos = get_train_logos()
        
        # Get appropriate logo based on schedule type
        if schedule_type == "arrivals":
            logo_html = train_logos.get(f"{train_type}_arrivals", train_logos.get(train_type, train_logos["default_arrivals"]))
        else:
            logo_html = train_logos.get(train_type, train_logos["default"])

        # Add row to table (new column order: Service, N°, Heure, Destination/Provenance)
        table_rows.append(f"""
        <tr class="schedule-row">
            <td class="carrier-column">{logo_html}</td>
            <td class="train-number">{train_number}</td>
            <td>
                <div class="schedule-time">{time_str[0]}</div>
            </td>
            <td>
                <div class="destination-origin">{destination_origin}</div>
            </td>
        </tr>""")

    # Insert the rows into the template
    template = template.replace('</tr>\n    </tbody>', f'{"".join(table_rows)}\n    </tbody>')

    return template
