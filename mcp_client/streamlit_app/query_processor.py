"""
Query processor module for the Multi-LLM Client Streamlit application.

This module handles the processing of user queries and manages the interaction
with the MultiLLMClient to get responses.
"""
import asyncio
import streamlit as st
from typing import Dict, Callable

# When imported as a module from the mcp-client directory
from streamlit_app.client_manager import get_client, get_loop
from streamlit_app.session_state import add_assistant_message, add_error_message
from streamlit_app.utils import generate_multiple_html_templates

async def create_token_callback(model_name: str, full_responses: Dict[str, str]) -> Callable:
    """
    Create a callback function for token streaming.

    Args:
        model_name: The model identifier
        full_responses: Dictionary to store the full responses

    Returns:
        Callback function for token streaming
    """
    async def token_callback(token, is_tool_call, tool_info=None):
        # Ignore tool calls, only collect text tokens
        if not is_tool_call and token.strip():
            full_responses[model_name] += token
        # Log tool calls for debugging in the requested format (same as CLI)
        if is_tool_call and tool_info:
            # Handle tool calls and results (same logic as CLI)
            if tool_info.get('is_result', False) and 'result' in tool_info:
                # Tool result - don't log here as it's too verbose
                pass
            else:
                # Tool call - format exactly like CLI
                tool_name = tool_info.get('tool_name', 'unknown')

                tool_args = tool_info.get('tool_args', {})
                import json
                print(f"\n[{model_name}] [Tool Call] {tool_name}({json.dumps(tool_args, indent=2)})")
    return token_callback


def process_query(query: str):
    """
    Process a user query and display the results.

    Args:
        query: The user query
    """
    client = get_client()
    loop = get_loop()

    if client is None or loop is None:
        st.error("Client not initialized. Please select a model and try again.")
        return

    # Create a dictionary to store the full responses for each model
    full_responses = {}
    callbacks = {}

    # Define a callback function for each model to collect the tokens
    # Access models through client.client in the new structure
    for model_key in client.client.models:
        full_responses[model_key] = ""

        # Create a callback for this model using the helper function
        callbacks[model_key] = asyncio.run(create_token_callback(model_key, full_responses))

    # System prompt is now handled in app.py to avoid updating it on every query
    # This ensures we maintain the conversation history while only updating the prompt when it changes

    # Run the query with streaming (always in parallel mode)
    future = asyncio.run_coroutine_threadsafe(
        client.stream_tokens(query, callbacks, sequential=False),
        loop
    )

    # Wait for the result
    result = future.result(timeout=120)  # Wait up to 120 seconds

    # Process each model's result
    model_responses = {}

    # Variables to store HTML content if found
    journey_html_content = None
    departures_html_content = None
    arrivals_html_content = None

    for model_key, model_result in result.items():
        if "error" in model_result:
            error_msg = f"Error: {model_result['error']}"
            print(f"Error for {model_key}: {error_msg}")
            model_responses[model_key] = {
                "response": error_msg,
                "provider": model_result.get("provider", "unknown"),
                "model_id": model_result.get("model_id", "unknown")
            }
            continue

        # Extract the response - use the collected tokens if available, otherwise use the result
        if model_key in full_responses and full_responses[model_key]:
            response = full_responses[model_key]
        else:
            response = model_result.get("response", "No response from the model.")

        # Get tool calls from the response
        tool_calls = model_result.get("tool_calls", [])

        # Log detailed tool call information in the requested format (same as CLI)
        if tool_calls:
            import json
            print(f"\n📊 [TOOL CALLS] Model: {model_key} - {len(tool_calls)} call(s)")
            for tool_call in tool_calls:
                tool_name = tool_call.get("tool_name", "unknown")

                # Get arguments from different possible fields (try tool_args first like CLI)
                args_dict = {}
                if "tool_args" in tool_call:
                    args_dict = tool_call.get("tool_args", {})
                elif "parameters" in tool_call:
                    args_dict = tool_call.get("parameters", {})
                elif "arguments" in tool_call:
                    args_dict = tool_call.get("arguments", {})

                # Format in the exact requested style (same as CLI)
                print(f"[Tool Call] {tool_name}({json.dumps(args_dict, indent=2)})")

        # Generate HTML for ALL tool calls that require HTML display
        if not journey_html_content and not departures_html_content and not arrivals_html_content and tool_calls:
            # Get selected tariff from session state
            selected_tariff = st.session_state.get("selected_tariff", "Tarif Normal")

            combined_html_content = generate_multiple_html_templates(tool_calls, selected_tariff)
            if combined_html_content:
                # Store in the first available variable for backward compatibility
                journey_html_content = combined_html_content
                print(f"✅ HTML content generated from tool calls for {model_key}")
            else:
                print(f"❌ No HTML content generated from tool calls for {model_key}")

        # Store the response
        model_responses[model_key] = {
            "response": response,
            "provider": model_key.split("/", 1)[0],
            "model_id": model_key.split("/", 1)[1],
            "tool_calls": tool_calls,  # Keep tool calls for reference
            "journey_html": journey_html_content,  # Store journey HTML if found
            "departures_html": departures_html_content,  # Store departures HTML if found
            "arrivals_html": arrivals_html_content  # Store arrivals HTML if found
        }

    # Display the response in a new chat message container
    with st.chat_message("assistant"):
        if "error" in model_responses:
            st.error(model_responses["error"])
            # Add error message to chat history
            add_error_message(model_responses["error"])
        else:
            # Define first_model before the conditional block to avoid UnboundLocalError
            first_model = next(iter(model_responses.keys())) if model_responses else None

            # Check if we have multiple models to display
            if len(model_responses) > 1:
                # Create tabs for each model
                tab_labels = []
                for model_key in model_responses.keys():
                    provider = model_responses[model_key].get("provider", "").capitalize()
                    model_id = model_responses[model_key].get("model_id", "")
                    tab_labels.append(f"{provider} ({model_id})")

                tabs = st.tabs(tab_labels)

                # Fill each tab with the corresponding model's response
                for i, (model_key, response_data) in enumerate(model_responses.items()):
                    with tabs[i]:
                        response_text = response_data.get("response", "No response")
                        st.markdown(response_text)

                        # Display journey HTML content if available
                        if journey_html_content:
                            st.components.v1.html(journey_html_content, height=750, scrolling=True)

                        # Display arrivals HTML content if available
                        elif arrivals_html_content:
                            st.components.v1.html(arrivals_html_content, height=1000, scrolling=True)

                        # Display departures HTML content if available
                        elif departures_html_content:
                            st.components.v1.html(departures_html_content, height=1000, scrolling=True)

            else:
                # Single model case - no tabs needed
                response_data = model_responses[first_model]
                st.markdown(response_data.get("response", "No response"))

                # Display journey HTML content if available
                if journey_html_content:
                    st.components.v1.html(journey_html_content, height=750, scrolling=True)

                # Display arrivals HTML content if available
                elif arrivals_html_content:
                    st.components.v1.html(arrivals_html_content, height=1000, scrolling=True)

                # Display departures HTML content if available
                elif departures_html_content:
                    st.components.v1.html(departures_html_content, height=1000, scrolling=True)

        # Add individual model responses to chat history
        for model_key, response_data in model_responses.items():
            response_text = response_data.get("response", "No response")

            # Add this model's response to chat history with metadata
            add_assistant_message(
                model_key=model_key,
                response_text=response_text,
                html_content=journey_html_content or departures_html_content or arrivals_html_content,  # Include HTML content if available
                tool_calls=response_data.get("tool_calls", []),
                query=query
            )
