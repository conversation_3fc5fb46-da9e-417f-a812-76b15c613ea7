"""
UI components for the Multi-LLM Client Streamlit application.

This module provides reusable UI components and display functions for the Streamlit interface.
"""
import streamlit as st
from typing import Dict, List, Any, Optional


def display_sidebar():
    """
    Display the sidebar with configuration options.

    Returns:
        Tuple containing:
        - List of selected models
        - System prompt text
    """
    with st.sidebar:
        st.title("TravelBot IA")

        # Model selection
        st.subheader("Sélection des modèles LLM")

        # Comprehensive list of available models
        available_models = [
            # Anthropic models
            "anthropic/claude-sonnet-4-0",
            "anthropic/claude-3-5-sonnet-20241022",
            "anthropic/claude-3-5-sonnet-latest",
            "anthropic/claude-3-opus-20240229",
            "anthropic/claude-3-haiku-20240307",

            # OpenAI models
            "openai/o3",
            "openai/gpt-4o",
            "openai/gpt-4o-mini",
            "openai/gpt-4-turbo",
            "openai/gpt-4",
            "openai/gpt-3.5-turbo",

            # Google Vertex AI models
            "vertex_ai/gemini-2.5-pro-preview-05-06",
            "vertex_ai/gemini-2.5-flash-preview-04-17"
        ]

        # Multi-select for models with search functionality
        selected_models = st.multiselect(
            "Sélectionnez les modèles LLM:",
            options=available_models,
            default=["anthropic/claude-sonnet-4-0"],
            help="Sélectionnez un ou plusieurs modèles. Vous pouvez aussi taper le nom d'un modèle personnalisé ci-dessous."
        )

        # Custom model input
        custom_model = st.text_input(
            "Modèle personnalisé (optionnel):",
            placeholder="provider/model_id (ex: openai/gpt-4o-2024-08-06)",
            help="Tapez le nom complet d'un modèle au format 'provider/model_id'. Il sera ajouté à la sélection si valide."
        )

        # Add custom model to selection if provided
        if custom_model and custom_model.strip():
            custom_model = custom_model.strip()
            # Basic validation: should contain a slash and not be empty
            if "/" in custom_model and len(custom_model.split("/")) == 2:
                provider, model_id = custom_model.split("/", 1)
                if provider and model_id and custom_model not in selected_models:
                    selected_models.append(custom_model)
                    st.success(f"✅ Modèle personnalisé ajouté: {custom_model}")
            else:
                st.error("❌ Format invalide. Utilisez: provider/model_id")

        # Display selected models
        if selected_models:
            # Create the info message with model details
            models_info = f"📋 Modèles sélectionnés: {len(selected_models)}\n\n"
            for model in selected_models:
                provider = model.split("/")[0]
                models_info += f"• {model} ({provider.capitalize()})\n\n"  # Ajout d'une ligne vide après chaque modèle

            st.info(models_info.strip())
        else:
            st.warning("⚠️ Aucun modèle sélectionné")

        # System prompt
        st.subheader("Message système")

        # Default system prompt with timedate placeholder
        default_prompt = "Vous êtes un assistant spécialisé dans les informations sur les trains SNCF. Date et heure actuelles : {{TIMEDATE}}"

        # System prompt text area
        system_prompt_text = st.text_area(
            "Message système:",
            default_prompt,
            height=100,
            help="Le message système inclut automatiquement la date et l'heure actuelles via {{TIMEDATE}}."
        )

        # Tariff selection
        st.subheader("Tarification")

        # Tariff options
        tariff_options = [
            "Tarif Normal",
            "Tarif Avantage",
            "Tarif Elève - Etudiant - Apprenti",
            "Tarif Réglementé"
        ]

        selected_tariff = st.selectbox(
            "Sélectionnez votre tarif:",
            tariff_options,
            index=0,  # Default to "Tarif Normal"
            help="Sélectionnez le type de tarif pour afficher les prix correspondants dans les résultats de voyage."
        )

        # No need to show a preview since the date is already included in the prompt

        return selected_models, system_prompt_text, selected_tariff


def display_conversation_history(messages: List[Dict[str, Any]]):
    """
    Display the conversation history with tabs for different model responses.

    Args:
        messages: List of message dictionaries from the session state
    """
    # First, group messages by conversation turn
    conversation_turns = []
    current_turn = {"user": None, "assistant": {}}

    for message in messages:
        if message["role"] == "user":
            # If we already have a complete turn, add it to the list
            if current_turn["user"] is not None:
                conversation_turns.append(current_turn)
                current_turn = {"user": None, "assistant": {}}

            # Set the user message for this turn
            current_turn["user"] = message
        elif message["role"] == "assistant":
            # If this is a model-specific message
            if "model_key" in message:
                model_key = message["model_key"]
                current_turn["assistant"][model_key] = message
            else:
                # Legacy format - add as a generic assistant message
                if "generic" not in current_turn["assistant"]:
                    current_turn["assistant"]["generic"] = []
                current_turn["assistant"]["generic"].append(message)

    # Add the last turn if it's not empty
    if current_turn["user"] is not None:
        conversation_turns.append(current_turn)

    # Display each conversation turn
    for turn in conversation_turns:
        # Display user message
        if turn["user"]:
            with st.chat_message("user"):
                st.markdown(turn["user"]["content"])

        # Display assistant responses
        if turn["assistant"]:
            with st.chat_message("assistant"):
                # Check if we have model-specific responses
                model_responses = {k: v for k, v in turn["assistant"].items() if k != "generic"}

                if model_responses:
                    # Create tabs for each model
                    tab_labels = []
                    for model_key in model_responses.keys():
                        if "/" in model_key:
                            provider, model_id = model_key.split("/", 1)
                            tab_labels.append(f"{provider.capitalize()} ({model_id})")
                        else:
                            tab_labels.append(model_key)

                    tabs = st.tabs(tab_labels)

                    # Fill each tab with the corresponding model's response
                    for i, (model_key, message) in enumerate(model_responses.items()):
                        with tabs[i]:
                            st.markdown(message["content"])

                            # Display HTML content if present
                            if message.get("html_content"):
                                # Ensure the HTML content is displayed with sufficient height
                                st.components.v1.html(message["html_content"], height=750, scrolling=True)

                # Handle generic assistant messages (legacy format)
                elif "generic" in turn["assistant"]:
                    for message in turn["assistant"]["generic"]:
                        # Regular message
                        st.markdown(message["content"])

                        # Display HTML content if present
                        if message.get("html_content"):
                            # Ensure the HTML content is displayed with sufficient height
                            st.components.v1.html(message["html_content"], height=750, scrolling=True)