"""
Tool for getting departure and arrival information from/to a station.
"""

from typing import Any, Dict, List, Optional, Literal
from mcp.types import Tool

from tools import SncfTools

from utils.sncf_api import make_sncf_api_request
from tools.autocomplete_places import autocomplete_places

async def get_station_schedules(
    station: str,
    schedule_type: Literal["departures", "arrivals"],
    datetime: str | None = None,
    data_freshness: str = "realtime",
    count: int = 10
) -> Any:
    """
    Get departures or arrivals from/to a station, and the disruptions that may affect them.

    Args:
        station: Exact station name or station code (stop_area:, stop_point:)
        schedule_type: Type of schedule to retrieve ("departures" or "arrivals")
        count: Number of schedules to return
        datetime: Optional time (format: YYYYMMDDThhmmss)
        data_freshness: Data freshness level (realtime, base_schedule, adapted_schedule)

    Returns:
        Dictionary with schedules and disruptions
    """
    # Check if already stop_area or stop_point
    if station.startswith("stop_area:") or station.startswith("stop_point:"):
        place = station
    else:
        # Only search for exact station names, not administrative regions
        place = await autocomplete_places(query=station, searched_type="stop_area", count=1)
        if place is None or not place:
            return f"Error: No station found with name '{station}'. Please use the exact station name or use autocomplete_places tool to find the correct station code."
        place = place[0]["id"]

    # Make the API request
    schedules = await make_sncf_api_request(
        f"{place.split(':')[0]}s/{place}/{schedule_type}?count={count}"
        f"&forbidden_uris%5B%5D=physical_mode%3ARapidTransit"
        f"&forbidden_uris%5B%5D=physical_mode%3ABus"
        f"&forbidden_uris%5B%5D=physical_mode%3ACoach"
        f"&forbidden_uris%5B%5D=physical_mode%3ATramway"
        f"{'&from_datetime=' + datetime if datetime else ''}"
        f"&data_freshness={data_freshness}"
    )

    if schedules is None:
        return f"Error: No {schedule_type} found"

    # Filter and simplify the schedules for LLM consumption
    result = {
        schedule_type: [],
        "disruptions": [],
        "station_name": station
    }

    # The key in the response depends on the schedule_type
    schedule_items = schedules[schedule_type]

    for item in schedule_items:
        simplified_item = {
            "display_informations": {
                "direction": item["display_informations"].get("direction", ""),
                "physical_mode": item["display_informations"].get("physical_mode", ""),
                "trip_short_name": item["display_informations"].get("trip_short_name", ""),
                "network": item["display_informations"].get("network", ""),
                "code": item["display_informations"].get("code", "")
            },
            "stop_point": {
                "name": item["stop_point"].get("name", ""),
                "physical_modes": item["stop_point"].get("physical_modes", []),
                "coord": item["stop_point"].get("coord", {}),
                "label": item["stop_point"].get("label", ""),
                "id": item["stop_point"].get("id", "")
            },
            "route": {
                "id": item["route"].get("id", ""),
                "name": item["route"].get("name", ""),
                "direction": {
                    "name": item["route"].get("direction", {}).get("name", "") if item["route"].get("direction") else ""
                }
            },
            "stop_date_time": {
                "arrival_date_time": item["stop_date_time"].get("arrival_date_time", ""),
                "departure_date_time": item["stop_date_time"].get("departure_date_time", ""),
                "base_arrival_date_time": item["stop_date_time"].get("base_arrival_date_time", ""),
                "base_departure_date_time": item["stop_date_time"].get("base_departure_date_time", "")
            }
        }

        result[schedule_type].append(simplified_item)

    for disruption in schedules["disruptions"]:
        simplified_disruption = {
            "id": disruption["id"],
            "application_periods": disruption["application_periods"],
            "impacted_objects": disruption["impacted_objects"]
        }

        if "messages" in disruption:
            simplified_disruption["messages"] = disruption["messages"]

        result["disruptions"].append(simplified_disruption)

    return result

def get_tool_definition(schedule_type: Literal["departures", "arrivals"]) -> Tool:
    """
    Get the Tool definition for station schedule tools (departures or arrivals).

    Args:
        schedule_type: Type of schedule ("departures" or "arrivals")

    Returns:
        Tool: The Tool definition
    """

    return Tool(
        name=SncfTools.GET_DEPARTURES.value if schedule_type == "departures" else SncfTools.GET_ARRIVALS.value,
        description=f"""Get real-time {schedule_type} information from a specific train station with disruption details.

🚂 **PRIMARY USE CASES**:
• View next trains {"departing from" if schedule_type == "departures" else "arriving at"} a specific station
• Check real-time delays and platform information
• Monitor disruptions affecting station operations
• Plan connections and transfers at stations
• Get detailed train information (numbers, destinations, times)

🏢 **STATION INPUT REQUIREMENTS**:
• **Use EXACT station names only**: "Paris Gare de Lyon", "Lyon Part-Dieu", "Marseille Saint-Charles".
• **NO city names**: Don't use "Paris", "Lyon", "Marseille" - these won't work. If you don't know the exact station name, ask the user to provide the exact station name.
• **Station codes accepted**: stop_area: or stop_point: codes from autocomplete_places
• **When station not found**: Use autocomplete_places tool to find exact station name/code
• **Multiple stations in city**: Always specify the exact station, never guess

⏰ **TIME FILTERING**:
• Default: Shows current and upcoming {schedule_type}
• Custom time: Format YYYYMMDDThhmmss (e.g., "20241215T143000")
• Always use future dates/times for planning

📊 **OUTPUT**: Returns detailed {schedule_type} with train numbers, times, destinations/origins, platforms, and real-time disruption information.""",
        inputSchema={
            "type": "object",
            "properties": {
                "station": {
                    "type": "string",
                    "description": f"🏢 EXACT station name required. Examples: 'Paris Gare de Lyon', 'Lyon Part-Dieu', 'Marseille Saint-Charles'. DO NOT use city names like 'Paris' or 'Lyon'. If station not found, use autocomplete_places tool to get station code (stop_area: or stop_point:)."
                },
                "datetime": {
                    "type": "string",
                    "description": f"⏰ Optional {schedule_type} time in format YYYYMMDDThhmmss. Examples: '20241215T143000' (Dec 15, 2024 at 14:30). If omitted, shows current and upcoming {schedule_type}. MUST be in the future.",
                    "default": None
                },
                "data_freshness": {
                    "type": "string",
                    "enum": ["realtime", "adapted_schedule"],
                    "description": "📡 Data source: 'realtime' (live data with delays), 'adapted_schedule' (includes comprehensive disruption information). Use 'adapted_schedule' for disruption details.",
                    "default": "realtime"
                }
            },
            "required": ["station"]
        }
    )
