"""
Tool for getting journey information between two stations.
"""

from typing import Any, Optional, Dict
from mcp.types import Tool

from tools import SncfTools
from tools.autocomplete_places import autocomplete_places

from utils.sncf_api import make_sncf_api_request
from utils.opendata import get_prices_from_opendata
from utils.commercial_modes_format import format_commercial_modes

async def get_journeys(
    from_station: str,
    to_station: str,
    datetime: str | None = None,
    # count: int = 5,
    classe: str = "2",
    data_freshness: str = "realtime",
    traveler_type: str = "standard",
    direct_path: str = "indifferent",
    commercial_modes: list[str] | None = None
) -> Any:
    """
    Get journeys between two stations, and the disruptions that may affect them.

    Args:
        from_station: Departure station name or code
        to_station: Arrival station name or code
        datetime: Optional departure time (format: YYYYMMDDThhmmss)
        count: Number of journeys to return
        classe: Travel class (1 or 2)
        data_freshness: Data freshness level (realtime, base_schedule, adapted_schedule)
        traveler_type: Type of traveler (standard, slow_walker, fast_walker, wheelchair, luggage)
        direct_path: Direct path preference (indifferent, none, only, only_with_alternatives)
        commercial_modes: List of commercial modes to filter by (e.g., ["OUIGO", "INOUI", ...])

    Returns:
        Dictionary with journeys and disruptions
    """
    count = 5
    # Check if already stop_area or stop_point
    if from_station.startswith("stop_area:") or from_station.startswith("stop_point:") or from_station.startswith("admin:fr:"):
        from_place = from_station
    else:
        from_place = await autocomplete_places(query=f"{from_station}%20", searched_type="administrative_region", count=1)
        if isinstance(from_place, str):
            from_place = await autocomplete_places(query=f"{from_station}%20", searched_type="stop_area", count=1)
            if isinstance(from_place, str):
                return "Error: No from place found"
        from_place = from_place[0]["id"]

    if to_station.startswith("stop_area:") or to_station.startswith("stop_point:") or to_station.startswith("admin:fr:"):
        to_place = to_station
    else:
        to_place = await autocomplete_places(query=f"{to_station}%20", searched_type="administrative_region", count=1)
        if isinstance(to_place, str):
            to_place = await autocomplete_places(query=f"{to_station}%20", searched_type="stop_area", count=1)
            if isinstance(to_place, str):
                return "Error: No to place found"
        to_place = to_place[0]["id"]

    # Format commercial modes for API request
    commercial_modes = format_commercial_modes(commercial_modes)

    journeys = await make_sncf_api_request(f"journeys?from={from_place}&to={to_place}&count={count}{'&datetime=' + datetime if datetime else ''}&data_freshness={data_freshness}&traveler_type={traveler_type}&direct_path={direct_path}&{'&allowed_id[]=' + '&allowed_id[]='.join(commercial_modes) if commercial_modes else ''}&forbidden_uris%5B%5D=physical_mode%3ARapidTransit&forbidden_uris%5B%5D=physical_mode%3ABus&forbidden_uris%5B%5D=physical_mode%3ACoach&forbidden_uris%5B%5D=physical_mode%3ATramway")
    if journeys is None:
        return "Error: No journeys found"

    # Filter and simplify the journeys for LLM consumption
    result = {
        "journeys": [],
        "disruptions": [],
        "from_station": from_station,
        "to_station": to_station
    }

    for journey in journeys["journeys"]:
        simplified_journey = {
            "departure": journey["departure_date_time"],
            "arrival": journey["arrival_date_time"],
            "duration": journey["duration"],  # Duration in seconds
            "nb_transfers": journey.get("nb_transfers", 0),
            "type": journey.get("type", ""),
            "sections": [],
            "journey_prices": {}
        }

        journey_prices = { "normal": "0 - 0" }

        # Process each section of the journey
        for section in journey["sections"]:
            simplified_section = {
                "from": section["from"]["name"] if "from" in section else "",
                "to": section["to"]["name"] if "to" in section else "",
                "departure_time": section.get("departure_date_time", ""),
                "arrival_time": section.get("arrival_date_time", "")
            }

            # Add mode-specific information
            if section["type"] == "public_transport":
                if "display_informations" in section:
                    info = section["display_informations"]
                    simplified_section["transport_info"] = {
                        "network": info.get("network", ""),
                        "code": info.get("code", ""),
                        "trip_short_name": info.get("trip_short_name", ""),
                        "direction": info.get("direction", ""),
                        "physical_mode": info.get("physical_mode", ""),
                        }
                    
                    section_prices = await get_prices_from_opendata(departure = section["from"]["stop_point"]["stop_area"]["id"], arrival = section["to"]["stop_point"]["stop_area"]["id"], classe = int(classe)) or ""
                
                    if section_prices:
                        """
                        map section_prices to journey_prices by summing the prices
                        section_prices = {
                            normal: "10 - 100",
                            tarif_reduit: "5 - 50"
                        }

                        journey_prices = {
                            normal: "10 - 100",
                            tarif_reduit: "5 - 50"
                        }

                        Adding the normal tariff if the section_prices does not have a special tariff
                        """
                        for tariff, price in section_prices.items():
                            if journey_prices == {}:
                                journey_prices = section_prices
                            elif tariff not in journey_prices:
                                # Sum the price with normal price in journey_prices
                                journey_min = float(price.split(' - ')[0]) + float(journey_prices["normal"].split(' - ')[0])
                                journey_max = float(price.split(' - ')[1]) + float(journey_prices["normal"].split(' - ')[1])
                                journey_prices[tariff] = f"{journey_min:.2f} - {journey_max:.2f}"
                            else:
                                journey_min = float(journey_prices[tariff].split(' - ')[0]) + float(price.split(' - ')[0])
                                journey_max = float(journey_prices[tariff].split(' - ')[1]) + float(price.split(' - ')[1])
                                journey_prices[tariff] = f"{journey_min:.2f} - {journey_max:.2f}"

                        
                
                if "route" in section:
                    simplified_section["route"] = section["route"].get("direction", "")

                simplified_journey["sections"].append(simplified_section)

        # Add the journey to the result after processing all its sections and add "€" symbol to the prices
        for tariff, price in journey_prices.items():
            journey_prices[tariff] = f"{price.split(' - ')[0]}€ - {price.split(' - ')[1]}€"
        simplified_journey["journey_prices"] = journey_prices
        result["journeys"].append(simplified_journey)

    # Process disruptions after all journeys have been processed
    for disruption in journeys.get("disruptions", []):
        try:
            simplified_disruption = {
                "id": disruption.get("id", "unknown"),
                "application_periods": disruption.get("application_periods", [])
            }

            # Safely access impacted_objects and pt_object
            if "impacted_objects" in disruption:
                impacted_objects = disruption["impacted_objects"]
                if isinstance(impacted_objects, dict) and "pt_object" in impacted_objects:
                    simplified_disruption["impacted_objects"] = impacted_objects["pt_object"]
                else:
                    simplified_disruption["impacted_objects"] = impacted_objects
            else:
                simplified_disruption["impacted_objects"] = []

            # Safely access messages
            if "messages" in disruption:
                messages = disruption["messages"]
                if isinstance(messages, dict) and "text" in messages:
                    simplified_disruption["messages"] = messages["text"]
                else:
                    simplified_disruption["messages"] = str(messages)

            result["disruptions"].append(simplified_disruption)
        except Exception as e:
            print(f"Error processing disruption: {e}")

    return result


def get_tool_definition() -> Tool:
    """
    Get the Tool definition for the get_journeys tool.

    Returns:
        Tool: The Tool definition
    """
    return Tool(
        name=SncfTools.GET_JOURNEYS.value,
        description="""Find train journeys between two locations in France with real-time schedules, prices, and disruption information.

🚂 **PRIMARY USE CASES**:
• Find train connections between cities/stations
• Get departure/arrival times and durations
• Check prices for different travel classes
• View real-time disruptions and delays
• Plan journeys with transfer information

📍 **LOCATION INPUT GUIDELINES**:
• **Exact station names**: Use ONLY when the user explicitly mentions the exact station (e.g., user says "Paris Gare de Lyon", "Lyon Part-Dieu")
• **City names**: Use when user mentions only the city or when you don't know the exact station (e.g., user says "Paris", "Lyon") - system auto-resolves to main stations.
• **DO NOT guess station names**: If user says "Paris", use "Paris" - don't assume "Paris Gare de Lyon"
• **Station codes**: Use if obtained from autocomplete_places (e.g., "stop_area:SNCF:87686006")
• **When to use autocomplete_places**: Only if location is not recognized or you need specific station codes

⏰ **TIME HANDLING**:
• Default: Uses current time for immediate departures
• Custom time: Format YYYYMMDDThhmmss (e.g., "20241215T143000" for Dec 15, 2024 at 14:30)
• Always use future dates/times - never past dates
• Consider current date/time when setting custom departure times

🚄 **COMMERCIAL MODES EXPLAINED**:
• **INOUI**: High-speed trains (TGV) - premium service, reservations required, fastest connections between major cities
• **OUIGO**: Low-cost high-speed trains - budget-friendly TGV service with mandatory reservations, limited luggage
• **OUIGO TRAIN CLASSIQUE**: Low-cost conventional trains - budget option for regional/intercity travel
• **TER**: Regional trains - local/regional service, no reservations needed, frequent stops
• **INTERCITE**: Intercity trains - medium-distance conventional trains connecting regions
• **INTERCITE DE NUIT**: Night trains - overnight service with sleeping accommodations

💡 **COMMERCIAL MODE USAGE TIPS**:
• Leave empty for all available options (recommended for most searches)
• Use specific modes when user has preferences (e.g., "only high-speed trains" → ["INOUI", "OUIGO"])
• Combine modes for flexible searches (e.g., ["INOUI", "TER"] for high-speed + regional options)
• Consider user's budget: OUIGO for budget, INOUI for comfort, TER for local travel

📊 **OUTPUT**: Returns structured journey data + generates HTML table for user display with train numbers, times, stations, duration, and prices.""",
        inputSchema={
            "type": "object",
            "properties": {
                "from_station": {
                    "type": "string",
                    "description": "🚉 Departure location. Use EXACTLY what the user provides: if they say 'Paris Gare de Lyon', use that; if they say 'Paris', use 'Paris' (don't guess the station). Examples: 'Paris' (city), 'Lyon' (city), 'Paris Gare de Lyon' (only if user specified).",
                },
                "to_station": {
                    "type": "string",
                    "description": "🎯 Arrival location. Use EXACTLY what the user provides: if they say 'Lyon Part-Dieu', use that; if they say 'Lyon', use 'Lyon' (don't guess the station). Examples: 'Lyon' (city), 'Nice' (city), 'Lyon Part-Dieu' (only if user specified).",
                },
                "datetime": {
                    "type": "string",
                    "description": "⏰ Departure date/time in format YYYYMMDDThhmmss. Examples: '20241215T143000' (Dec 15, 2024 at 14:30), '20241220T080000' (Dec 20, 2024 at 08:00). If omitted, uses current time. MUST be in the future.",
                    "default": None
                },
                "classe": {
                    "type": "string",
                    "enum": ["1", "2"],
                    "description": "🎫 Travel class: 1 (first class, premium) or 2 (second class, standard). Affects pricing and comfort level.",
                    "default": "2"
                },
                "data_freshness": {
                    "type": "string",
                    "enum": ["realtime", "adapted_schedule"],
                    "description": "📡 Data source: 'realtime' (live data, delays), 'adapted_schedule' (includes disruption info and delays). Use 'adapted_schedule' for disruption details.",
                    "default": "realtime"
                },
                "traveler_type": {
                    "type": "string",
                    "enum": ["standard", "slow_walker", "fast_walker", "wheelchair", "luggage"],
                    "description": "👤 Traveler profile affecting connection times: 'standard' (normal), 'slow_walker' (extra time), 'fast_walker' (quick), 'wheelchair' (accessible), 'luggage' (extra time).",
                    "default": "standard"
                },
                "direct_path": {
                    "type": "string",
                    "enum": ["indifferent", "none", "only", "only_with_alternatives"],
                    "description": "🔄 Connection preference: 'indifferent' (any), 'none' (no direct preference), 'only' (direct only), 'only_with_alternatives' (direct + backup options).",
                    "default": "indifferent"
                },
                "commercial_modes": {
                    "type": "array",
                    "items": {
                        "type": "string",
                        "enum": ["INOUI", "OUIGO", "OUIGO TRAIN CLASSIQUE", "TER", "INTERCITE", "INTERCITE DE NUIT"],
                        "description": "🚄 Commercial mode options: 'INOUI' (high-speed TGV premium), 'OUIGO' (low-cost high-speed), 'OUIGO TRAIN CLASSIQUE' (low-cost conventional), 'TER' (regional), 'INTERCITE' (intercity), 'INTERCITE DE NUIT' (night trains)."
                    },
                    "description": "🚄 Filter by train types. Examples: ['INOUI'] for premium high-speed only, ['OUIGO', 'OUIGO TRAIN CLASSIQUE'] for budget options, ['TER'] for regional trains only. Leave empty for all available modes (recommended).",
                    "default": []
                }
            },
            "required": ["from_station", "to_station"]
        }
    )