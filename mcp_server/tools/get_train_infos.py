"""
Tool for getting train information.
"""

from typing import Any, Dict, List, Optional
import os
from mcp.types import Tool

from tools import SncfTools

from utils.sncf_api import make_sncf_api_request

async def get_train_infos(
    train_number: str,
    data_freshness: str = "realtime",
    since: str | None = None,
    until: str | None = None,
) -> Any:
    """
    Get detailed information about a specific train including real-time status, route, and disruptions.

    Args:
        train_number: Train number (numeric part only, e.g., "6651", "17208")
        data_freshness: Data freshness level (realtime, base_schedule, adapted_schedule)
        since: Optional start time filter (format: YYYYMMDDThhmmss)
        until: Optional end time filter (format: YYYYMMDDThhmmss)

    Returns:
        Dictionary with detailed train information, route, and disruptions
    """
    count = 2

    request_url = f"vehicle_journeys?headsign={train_number}&count={count}&data_freshness={data_freshness}{'&since=' + since if since else ''}{'&until=' + until if until else ''}"
    response = await make_sncf_api_request(request_url)

    if response is None:
        return "Error: No response from SNCF API"

    if "vehicle_journeys" not in response or not response["vehicle_journeys"]:
        return f"Error: No train found with number {train_number}. This tool works best with TGV train numbers. For other train types, try using get_journeys or station departure/arrival tools."

    result = {
        "train_infos": response["vehicle_journeys"],
        "disruptions": []
    }

    # Process disruptions with better error handling
    for disruption in response.get("disruptions", []):
        try:
            simplified_disruption = {
                "id": disruption.get("id", "unknown"),
                "application_periods": disruption.get("application_periods", [])
            }

            # Safely access impacted_objects
            if "impacted_objects" in disruption:
                impacted_objects = disruption["impacted_objects"]
                if isinstance(impacted_objects, dict) and "pt_object" in impacted_objects:
                    simplified_disruption["impacted_objects"] = impacted_objects["pt_object"]
                else:
                    simplified_disruption["impacted_objects"] = impacted_objects
            else:
                simplified_disruption["impacted_objects"] = []

            # Safely access messages
            if "messages" in disruption:
                messages = disruption["messages"]
                if isinstance(messages, dict) and "text" in messages:
                    simplified_disruption["messages"] = messages["text"]
                else:
                    simplified_disruption["messages"] = str(messages)

            result["disruptions"].append(simplified_disruption)
        except Exception as e:
            print(f"Error processing disruption: {e}")

    return result


def get_tool_definition() -> Tool:
    """
    Get the Tool definition for the get_train_infos tool.

    Returns:
        Tool: The Tool definition
    """
    return Tool(
        name=SncfTools.GET_TRAIN_INFOS.value,
        description="""Get detailed information about a specific train including real-time status, route, stops, and disruptions.

🚂 **PRIMARY USE CASES**:
• Track a specific train's real-time status and delays
• Get complete route information with all stops
• Check disruptions affecting a particular train
• Monitor train progress during travel
• Verify train existence and schedule details

⚠️ **IMPORTANT LIMITATIONS**:
• **Works with TGV trains only**
• **Alternative**: For non-TGV trains, consider using get_journeys or station departure/arrival tools

🔢 **TRAIN NUMBER INPUT**:
• Use ONLY the numeric part of the train number
• Extract the number from user input: "TGV 6651" → use "6651"
• Always provide just the number, never include "TGV" for instance

⏰ **TIME FILTERING**:
• Default: Shows current and upcoming journeys for the train
• Use 'since' and 'until' to filter by specific time periods
• Format: YYYYMMDDThhmmss (e.g., "20241215T143000")
• Useful for historical data or future planning

📊 **OUTPUT**: Returns detailed train information including route, stops, real-time status, and any disruptions affecting the train.""",
        inputSchema={
            "type": "object",
            "properties": {
                "train_number": {
                    "type": "string",
                    "description": "🚂 Train number (numeric part only). Extract the number from user input: 'TGV 6651' → '6651', 'TER 17208' → '17208'. Works best with TGV trains. For TER/regional trains, consider using get_journeys instead.",
                },
                "data_freshness": {
                    "type": "string",
                    "enum": ["realtime", "adapted_schedule"],
                    "description": "📡 Data source: 'realtime' (live data with delays), 'adapted_schedule' (includes disruption details). Use 'adapted_schedule' for comprehensive disruption information.",
                    "default": "realtime"
                },
                "since": {
                    "type": "string",
                    "description": "⏰ Optional start time filter in format YYYYMMDDThhmmss. Example: '20241215T143000' (Dec 15, 2024 at 14:30). Filters results to show journeys from this time onwards.",
                },
                "until": {
                    "type": "string",
                    "description": "⏰ Optional end time filter in format YYYYMMDDThhmmss. Example: '20241215T180000' (Dec 15, 2024 at 18:00). Filters results to show journeys until this time.",
                }
            },
            "required": ["train_number"]
        }
    )
